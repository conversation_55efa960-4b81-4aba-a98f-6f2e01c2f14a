/**
 * @jest-environment jsdom
 */

import { renderHook, act } from '@testing-library/react';
import { useFormPersistence } from '../use-form-persistence';
import type {
  ContractorStep1FormValues,
  ContractorStep2FormValues,
  ContractorStep3FormValues,
} from '../../schemas/contractor-onboarding-schemas';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('useFormPersistence', () => {
  beforeEach(() => {
    localStorageMock.clear();
  });

  it('should save and load step 1 data', () => {
    const { result } = renderHook(() => useFormPersistence());

    const step1Data: ContractorStep1FormValues = {
      fullName: 'John <PERSON>e',
      icNumber: '123456-78-9012',
      phoneNumber: '01234567890',
    };

    act(() => {
      result.current.saveStep1Data(step1Data);
    });

    const loadedData = result.current.loadAllFormData();
    expect(loadedData.step1Data).toEqual(step1Data);
  });

  it('should save and load step 2 data', () => {
    const { result } = renderHook(() => useFormPersistence());

    const step2Data: ContractorStep2FormValues = {
      companyRegistrationType: 'create',
      specialCode: '',
    };

    act(() => {
      result.current.saveStep2Data(step2Data);
    });

    const loadedData = result.current.loadAllFormData();
    expect(loadedData.step2Data).toEqual(step2Data);
  });

  it('should save and load step 3 data', () => {
    const { result } = renderHook(() => useFormPersistence());

    const step3Data: ContractorStep3FormValues = {
      company_name: 'Test Company',
      company_type: 'COMPETENT_FIRM',
      company_hotline: '03-12345678',
      oem_name: 'Test OEM',
      code: 'TEST123',
    };

    act(() => {
      result.current.saveStep3Data(step3Data);
    });

    const loadedData = result.current.loadAllFormData();
    expect(loadedData.step3Data).toEqual(step3Data);
  });

  it('should save and load current step', () => {
    const { result } = renderHook(() => useFormPersistence());

    act(() => {
      result.current.saveCurrentStep(2);
    });

    const loadedData = result.current.loadAllFormData();
    expect(loadedData.currentStep).toBe(2);
  });

  it('should clear all form data', () => {
    const { result } = renderHook(() => useFormPersistence());

    // Save some data first
    const step1Data: ContractorStep1FormValues = {
      fullName: 'John Doe',
      icNumber: '123456-78-9012',
      phoneNumber: '01234567890',
    };

    act(() => {
      result.current.saveStep1Data(step1Data);
      result.current.saveCurrentStep(2);
    });

    // Verify data is saved
    let loadedData = result.current.loadAllFormData();
    expect(loadedData.step1Data).toEqual(step1Data);
    expect(loadedData.currentStep).toBe(2);

    // Clear data
    act(() => {
      result.current.clearFormData();
    });

    // Verify data is cleared
    loadedData = result.current.loadAllFormData();
    expect(loadedData.step1Data).toBeNull();
    expect(loadedData.step2Data).toBeNull();
    expect(loadedData.step3Data).toBeNull();
    expect(loadedData.currentStep).toBe(1);
  });

  it('should detect persisted data', () => {
    const { result } = renderHook(() => useFormPersistence());

    // Initially no persisted data
    expect(result.current.hasPersistedData()).toBe(false);

    // Save some data
    const step1Data: ContractorStep1FormValues = {
      fullName: 'John Doe',
      icNumber: '123456-78-9012',
      phoneNumber: '01234567890',
    };

    act(() => {
      result.current.saveStep1Data(step1Data);
    });

    // Should now detect persisted data
    expect(result.current.hasPersistedData()).toBe(true);
  });

  it('should handle localStorage errors gracefully', () => {
    const { result } = renderHook(() => useFormPersistence());

    // Mock localStorage to throw an error
    const originalSetItem = localStorageMock.setItem;
    localStorageMock.setItem = jest.fn(() => {
      throw new Error('Storage quota exceeded');
    });

    const step1Data: ContractorStep1FormValues = {
      fullName: 'John Doe',
      icNumber: '123456-78-9012',
      phoneNumber: '01234567890',
    };

    // Should not throw an error
    expect(() => {
      act(() => {
        result.current.saveStep1Data(step1Data);
      });
    }).not.toThrow();

    // Restore original function
    localStorageMock.setItem = originalSetItem;
  });
});
