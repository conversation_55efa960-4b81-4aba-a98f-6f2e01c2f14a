'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    AlertTriangle,
    ArrowRight,
    Building2,
    CheckCircle2,
    Clock,
    FileX,
    UserX
} from 'lucide-react';
import { useRouter } from 'next/navigation';

interface IncompleteProfileCardProps {
  /**
   * Type of user role
   */
  userRole?: string;
  /**
   * Whether user is a contractor
   */
  isContractor?: boolean;
  /**
   * Whether onboarding is completed
   */
  onboardingCompleted?: boolean;
  /**
   * Whether contractor data exists
   */
  hasContractorData?: boolean;
  /**
   * Custom message to display
   */
  customMessage?: string;
  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Reusable component for displaying incomplete profile states with call-to-action buttons
 * Follows shadcn/ui design patterns and is fully responsive
 */
export function IncompleteProfileCard({
  userRole,
  isContractor,
  onboardingCompleted,
  hasContractorData,
  customMessage,
  className,
}: IncompleteProfileCardProps) {
  const router = useRouter();

  // Determine the incomplete state and appropriate messaging
  const getProfileState = () => {
    if (isContractor && !onboardingCompleted) {
      return {
        title: 'Contractor Registration Incomplete',
        description:
          'Complete your contractor profile setup to access all features. Your progress has been saved and you can continue where you left off.',
        icon: <UserX className="h-6 w-6 text-amber-500" />,
        status: 'pending',
        actionText: 'Continue Registration',
        actionPath: '/profile',
        secondaryActionText: 'Back to Registration',
        secondaryActionPath: '/profile?mode=registration',
        showBackToRegistration: true,
      };
    }

    if (isContractor && onboardingCompleted && !hasContractorData) {
      return {
        title: 'Profile Data Missing',
        description:
          'Your contractor profile data could not be loaded. Please contact support or retry registration.',
        icon: <FileX className="h-6 w-6 text-red-500" />,
        status: 'error',
        actionText: 'Retry Setup',
        actionPath: '/profile',
        secondaryActionText: 'Contact Support',
        secondaryActionPath: '/support',
      };
    }

    // Generic incomplete state
    return {
      title: 'Profile Setup Required',
      description:
        customMessage ||
        'Your profile information is incomplete. Please complete your setup to continue.',
      icon: <Building2 className="h-6 w-6 text-blue-500" />,
      status: 'incomplete',
      actionText: 'Complete Setup',
      actionPath: '/profile',
      secondaryActionText: 'View Projects',
      secondaryActionPath: '/projects',
    };
  };

  const profileState = getProfileState();

  // Handle primary action
  const handlePrimaryAction = () => {
    router.push(profileState.actionPath);
  };

  // Handle secondary action
  const handleSecondaryAction = () => {
    if (profileState.secondaryActionPath.startsWith('http')) {
      // External link
      window.open(profileState.secondaryActionPath, '_blank');
    } else {
      router.push(profileState.secondaryActionPath);
    }
  };

  return (
    <Card className={`w-full max-w-2xl mx-auto ${className || ''}`}>
      <CardHeader className="pb-4">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0 p-2 bg-muted rounded-lg">
            {profileState.icon}
          </div>
          <div className="flex-1 space-y-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl font-semibold">
                {profileState.title}
              </CardTitle>
              <Badge
                variant={
                  profileState.status === 'pending'
                    ? 'secondary'
                    : profileState.status === 'error'
                      ? 'destructive'
                      : 'outline'
                }
                className="ml-2"
              >
                {profileState.status === 'pending' && (
                  <>
                    <Clock className="h-3 w-3 mr-1" />
                    Pending
                  </>
                )}
                {profileState.status === 'error' && (
                  <>
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Error
                  </>
                )}
                {profileState.status === 'incomplete' && (
                  <>
                    <FileX className="h-3 w-3 mr-1" />
                    Incomplete
                  </>
                )}
              </Badge>
            </div>
            <p className="text-muted-foreground text-sm leading-relaxed">
              {profileState.description}
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-2">
        {/* Progress indicator for contractors */}
        {isContractor && !onboardingCompleted && (
          <div className="mb-6 p-4 bg-muted/50 rounded-lg">
            <h4 className="text-sm font-medium mb-3 flex items-center">
              <CheckCircle2 className="h-4 w-4 mr-2 text-muted-foreground" />
              Registration Progress
            </h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span>Personal Information</span>
                <Badge variant="outline">Incomplete</Badge>
              </div>{' '}
              <div className="flex items-center justify-between text-xs">
                <span>Company Setup</span>
                <Badge variant="outline">Pending</Badge>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span>Document Upload</span>
                <Badge variant="outline">Pending</Badge>
              </div>
            </div>
          </div>
        )}

        {/* Current role information */}
        <div className="mb-6 p-4 border rounded-lg bg-card">
          <h4 className="text-sm font-medium mb-2">Current Status</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-xs">
            <div>
              <span className="text-muted-foreground">Role:</span>
              <span className="ml-2 font-medium">{userRole || 'Unknown'}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Onboarding:</span>
              <span className="ml-2 font-medium">
                {onboardingCompleted ? 'Complete' : 'Incomplete'}
              </span>
            </div>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            onClick={handlePrimaryAction}
            className="flex-1 sm:flex-none"
            size="lg"
          >
            {profileState.actionText}
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>

          <Button
            variant="outline"
            onClick={handleSecondaryAction}
            className="flex-1 sm:flex-none"
            size="lg"
          >
            {profileState.secondaryActionText}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
