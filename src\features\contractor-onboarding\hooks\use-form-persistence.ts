import { useCallback, useEffect } from 'react';
import type {
  ContractorStep1FormValues,
  ContractorStep2FormValues,
  ContractorStep3FormValues,
} from '../schemas/contractor-onboarding-schemas';

// Storage keys for form data
const STORAGE_KEYS = {
  STEP_1: 'contractor_onboarding_step1',
  STEP_2: 'contractor_onboarding_step2',
  STEP_3: 'contractor_onboarding_step3',
  CURRENT_STEP: 'contractor_onboarding_current_step',
} as const;

export interface FormPersistenceData {
  step1Data: ContractorStep1FormValues | null;
  step2Data: ContractorStep2FormValues | null;
  step3Data: ContractorStep3FormValues | null;
  currentStep: number;
}

/**
 * Hook to persist and restore contractor onboarding form data
 * Uses localStorage to maintain form state across page refreshes and navigation
 */
export function useFormPersistence() {
  // Save form data to localStorage
  const saveFormData = useCallback((key: keyof typeof STORAGE_KEYS, data: any) => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(STORAGE_KEYS[key], JSON.stringify(data));
      } catch (error) {
        console.warn('Failed to save form data to localStorage:', error);
      }
    }
  }, []);

  // Load form data from localStorage
  const loadFormData = useCallback(<T>(key: keyof typeof STORAGE_KEYS): T | null => {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(STORAGE_KEYS[key]);
        return stored ? JSON.parse(stored) : null;
      } catch (error) {
        console.warn('Failed to load form data from localStorage:', error);
        return null;
      }
    }
    return null;
  }, []);

  // Clear all form data from localStorage
  const clearFormData = useCallback(() => {
    if (typeof window !== 'undefined') {
      try {
        Object.values(STORAGE_KEYS).forEach(key => {
          localStorage.removeItem(key);
        });
      } catch (error) {
        console.warn('Failed to clear form data from localStorage:', error);
      }
    }
  }, []);

  // Load all persisted form data
  const loadAllFormData = useCallback((): FormPersistenceData => {
    return {
      step1Data: loadFormData<ContractorStep1FormValues>('STEP_1'),
      step2Data: loadFormData<ContractorStep2FormValues>('STEP_2'),
      step3Data: loadFormData<ContractorStep3FormValues>('STEP_3'),
      currentStep: loadFormData<number>('CURRENT_STEP') || 1,
    };
  }, [loadFormData]);

  // Save step 1 data
  const saveStep1Data = useCallback((data: ContractorStep1FormValues) => {
    saveFormData('STEP_1', data);
  }, [saveFormData]);

  // Save step 2 data
  const saveStep2Data = useCallback((data: ContractorStep2FormValues) => {
    saveFormData('STEP_2', data);
  }, [saveFormData]);

  // Save step 3 data
  const saveStep3Data = useCallback((data: ContractorStep3FormValues) => {
    saveFormData('STEP_3', data);
  }, [saveFormData]);

  // Save current step
  const saveCurrentStep = useCallback((step: number) => {
    saveFormData('CURRENT_STEP', step);
  }, [saveFormData]);

  // Check if there's any persisted data
  const hasPersistedData = useCallback((): boolean => {
    const data = loadAllFormData();
    return !!(data.step1Data || data.step2Data || data.step3Data || data.currentStep > 1);
  }, [loadAllFormData]);

  return {
    saveStep1Data,
    saveStep2Data,
    saveStep3Data,
    saveCurrentStep,
    loadAllFormData,
    clearFormData,
    hasPersistedData,
  };
}
